#!/usr/bin/env python3
"""
Live Trading Bot with Oanda API Integration
Fetches live market data and provides trading signals every minute
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
from strategy_engine import StrategyEngine
from utils import (
    fetch_live_candles, get_current_time_info, print_colored,
    print_header, print_table_row, format_signal_output, validate_pair, clear_screen
)
from config import CURRENCY_PAIRS, TRADING_CONFIG, DISPLAY_CONFIG

class LiveTradingBot:
    def __init__(self):
        """Initialize the live trading bot"""
        self.strategy_engine = StrategyEngine()
        self.running = False
        self.pairs = CURRENCY_PAIRS.copy()
        
    def start(self):
        """Start the live trading bot"""
        print_header("🚀 LIVE TRADING BOT STARTED")
        print_colored(f"📊 Monitoring {len(self.pairs)} currency pairs", "INFO")
        print_colored(f"⏰ Fetching data every {TRADING_CONFIG['FETCH_INTERVAL']} seconds", "INFO")
        print_colored(f"🎯 Minimum confidence: {TRADING_CONFIG['MIN_CONFIDENCE']*100}%", "INFO")
        print()
        
        self.running = True
        
        # Start the main trading loop
        self.trading_loop()
    
    def stop(self):
        """Stop the trading bot"""
        self.running = False
        print_colored("\n🛑 Trading bot stopped", "WARNING")
    
    def trading_loop(self):
        """Main trading loop"""
        while self.running:
            try:
                time_info = get_current_time_info()
                
                # Wait until 2 seconds before next minute
                if time_info['seconds_to_next_minute'] > 2:
                    sleep_time = time_info['seconds_to_next_minute'] - 2
                    print_colored(f"⏳ Waiting {sleep_time} seconds until next scan...", "INFO")
                    time.sleep(sleep_time)
                
                # Perform market scan
                self.scan_markets()
                
                # Wait for next minute
                time.sleep(62)  # Wait a bit more than a minute to ensure new candle
                
            except KeyboardInterrupt:
                print_colored("\n⚠️  Interrupted by user", "WARNING")
                break
            except Exception as e:
                print_colored(f"❌ Error in trading loop: {str(e)}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def scan_markets(self):
        """Scan all currency pairs for trading signals"""
        clear_screen()  # Clear screen before each scan
        current_time = datetime.now()
        print_header(f"📊 MARKET SCAN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Display header line with different colors for each column
        header_line = (
            f"{'💱 PAIR':<15}   |   "
            f"{'📅 DATE':<12}   |   "
            f"{'🕐 TIME':<10}   |   "
            f"{'📈📉 DIRECTION':<12}   |   "
            f"{'🎯 CONFIDENCE':<12}   |   "
            f"{'💰 PRICE':<12}   |   "
            f"{'🔧 STRATEGY':<15}"
        )
        print_colored(header_line, "HEADER", bold=True)
        print_colored("=" * 120, "HEADER")

        signals_found = 0

        for pair in self.pairs:
            try:
                # Fetch live data
                df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])

                if df is not None and len(df) > 50:
                    # Evaluate strategies
                    signal_data = self.strategy_engine.evaluate_all_strategies(df)

                    # Format output
                    formatted_output = format_signal_output(pair, signal_data)

                    # Display result in horizontal format
                    if signal_data['signal'] != 'HOLD':
                        signals_found += 1
                        # Log signal in horizontal format
                        self.log_signal(formatted_output, signal_data)
                    else:
                        # No signal found - show in horizontal format with different colors
                        pair_colored = f"\033[96m💱 {pair:<12}\033[0m"  # Cyan for pair
                        date_colored = f"\033[93m📅 {formatted_output['date']:<12}\033[0m"  # Yellow for date
                        time_colored = f"\033[95m🕐 {formatted_output['time']:<10}\033[0m"  # Magenta for time
                        direction_colored = f"\033[37m⚪ {'NO SIGNAL':<10}\033[0m"  # White for no signal
                        confidence_colored = f"\033[37m🎯 {'-':<10}\033[0m"  # White for no confidence
                        price_colored = f"\033[97m💰 {formatted_output['price']:<12}\033[0m"  # White for price
                        strategy_colored = f"\033[37m🔧 {'-':<15}\033[0m"  # White for no strategy

                        no_signal_line = f"{pair_colored}   |   {date_colored}   |   {time_colored}   |   {direction_colored}   |   {confidence_colored}   |   {price_colored}   |   {strategy_colored}"
                        print(no_signal_line)
                else:
                    # Data fetch failed
                    pair_colored = f"\033[96m💱 {pair:<12}\033[0m"  # Cyan for pair
                    date_colored = f"\033[93m📅 {current_time.strftime('%Y-%m-%d'):<12}\033[0m"  # Yellow for date
                    time_colored = f"\033[95m🕐 {current_time.strftime('%H:%M:%S'):<10}\033[0m"  # Magenta for time
                    direction_colored = f"\033[91m❌ {'ERROR':<10}\033[0m"  # Red for error
                    confidence_colored = f"\033[91m⚠️ {'-':<10}\033[0m"  # Red for error
                    price_colored = f"\033[91m💰 {'ERROR':<12}\033[0m"  # Red for error
                    strategy_colored = f"\033[91m🔧 {'NO DATA':<15}\033[0m"  # Red for error

                    error_line = f"{pair_colored}   |   {date_colored}   |   {time_colored}   |   {direction_colored}   |   {confidence_colored}   |   {price_colored}   |   {strategy_colored}"
                    print(error_line)

            except Exception as e:
                print_colored(f"❌ Error scanning {pair}: {str(e)}", "ERROR")

        # Summary
        print()
        if signals_found > 0:
            print_colored(f"✅ Found {signals_found} trading signals", "SUCCESS", bold=True)
        else:
            print_colored("ℹ️  No trading signals found", "INFO")
        print()
    
    def log_signal(self, formatted_output, signal_data):
        """Log signal information in horizontal format with different colors and better spacing"""
        # Get direction emoji and color
        if signal_data['signal'] == 'BUY':
            direction_emoji = "📈"
            direction_color = "BUY"
        else:
            direction_emoji = "📉"
            direction_color = "SELL"

        # Create colored parts with more spacing
        pair_colored = f"\033[96m💱 {formatted_output['pair']:<12}\033[0m"  # Cyan for pair
        date_colored = f"\033[93m📅 {formatted_output['date']:<12}\033[0m"  # Yellow for date
        time_colored = f"\033[95m🕐 {formatted_output['time']:<10}\033[0m"  # Magenta for time

        if signal_data['signal'] == 'BUY':
            direction_colored = f"\033[92m{direction_emoji} {signal_data['signal']:<10}\033[0m"  # Green for BUY
        else:
            direction_colored = f"\033[91m{direction_emoji} {signal_data['signal']:<10}\033[0m"  # Red for SELL

        confidence_colored = f"\033[94m🎯 {formatted_output['confidence']:<10}\033[0m"  # Blue for confidence
        price_colored = f"\033[97m💰 {formatted_output['price']:<12}\033[0m"  # White for price
        strategy_colored = f"\033[90m🔧 {signal_data['strategy']:<15}\033[0m"  # Gray for strategy

        # Create the formatted line with more spacing
        signal_line = f"{pair_colored}   |   {date_colored}   |   {time_colored}   |   {direction_colored}   |   {confidence_colored}   |   {price_colored}   |   {strategy_colored}"
        print(signal_line)

def main():
    """Main function"""
    try:
        # Create and start the trading bot
        bot = LiveTradingBot()
        
        print_colored("🤖 Live Trading Bot Initializing...", "INFO", bold=True)
        print_colored("Press Ctrl+C to stop the bot", "WARNING")
        print()
        
        # Start the bot
        bot.start()
        
    except KeyboardInterrupt:
        print_colored("\n👋 Goodbye! Trading bot stopped.", "INFO")
    except Exception as e:
        print_colored(f"❌ Fatal error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()

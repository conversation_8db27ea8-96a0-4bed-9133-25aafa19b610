#!/usr/bin/env python3
"""
Live Trading Bot with Oanda API Integration
Fetches live market data and provides trading signals every minute
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
from strategy_engine import StrategyEngine
from utils import (
    fetch_live_candles, get_current_time_info, print_colored, 
    print_header, print_table_row, format_signal_output, validate_pair
)
from config import CURRENCY_PAIRS, TRADING_CONFIG, DISPLAY_CONFIG

class LiveTradingBot:
    def __init__(self):
        """Initialize the live trading bot"""
        self.strategy_engine = StrategyEngine()
        self.running = False
        self.pairs = CURRENCY_PAIRS.copy()
        
    def start(self):
        """Start the live trading bot"""
        print_header("🚀 LIVE TRADING BOT STARTED")
        print_colored(f"📊 Monitoring {len(self.pairs)} currency pairs", "INFO")
        print_colored(f"⏰ Fetching data every {TRADING_CONFIG['FETCH_INTERVAL']} seconds", "INFO")
        print_colored(f"🎯 Minimum confidence: {TRADING_CONFIG['MIN_CONFIDENCE']*100}%", "INFO")
        print()
        
        self.running = True
        
        # Start the main trading loop
        self.trading_loop()
    
    def stop(self):
        """Stop the trading bot"""
        self.running = False
        print_colored("\n🛑 Trading bot stopped", "WARNING")
    
    def trading_loop(self):
        """Main trading loop"""
        while self.running:
            try:
                time_info = get_current_time_info()
                
                # Wait until 2 seconds before next minute
                if time_info['seconds_to_next_minute'] > 2:
                    sleep_time = time_info['seconds_to_next_minute'] - 2
                    print_colored(f"⏳ Waiting {sleep_time} seconds until next scan...", "INFO")
                    time.sleep(sleep_time)
                
                # Perform market scan
                self.scan_markets()
                
                # Wait for next minute
                time.sleep(62)  # Wait a bit more than a minute to ensure new candle
                
            except KeyboardInterrupt:
                print_colored("\n⚠️  Interrupted by user", "WARNING")
                break
            except Exception as e:
                print_colored(f"❌ Error in trading loop: {str(e)}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def scan_markets(self):
        """Scan all currency pairs for trading signals"""
        current_time = datetime.now()
        print_header(f"📊 MARKET SCAN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        signals_found = 0

        for pair in self.pairs:
            try:
                # Fetch live data
                df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])

                if df is not None and len(df) > 50:
                    # Evaluate strategies
                    signal_data = self.strategy_engine.evaluate_all_strategies(df)

                    # Format output
                    formatted_output = format_signal_output(pair, signal_data)

                    # Display result in horizontal format
                    if signal_data['signal'] != 'HOLD':
                        signals_found += 1
                        # Log signal in horizontal format
                        self.log_signal(formatted_output, signal_data)
                    else:
                        # No signal found - show in horizontal format with emojis
                        pair_part = f"💱 {pair:<10}"
                        date_part = f"📅 {formatted_output['date']}"
                        time_part = f"🕐 {formatted_output['time']}"
                        price_part = f"💰 {formatted_output['price']:<10}"
                        status_part = f"⚪ NO SIGNAL"

                        no_signal_line = f"{pair_part} | {date_part} | {time_part} | {price_part} | {status_part}"
                        print_colored(no_signal_line, "INFO")
                else:
                    # Data fetch failed
                    pair_part = f"💱 {pair:<10}"
                    date_part = f"📅 {current_time.strftime('%Y-%m-%d')}"
                    time_part = f"🕐 {current_time.strftime('%H:%M:%S')}"
                    error_part = f"❌ ERROR"
                    status_part = f"⚠️  NO DATA"

                    error_line = f"{pair_part} | {date_part} | {time_part} | {error_part} | {status_part}"
                    print_colored(error_line, "ERROR")

            except Exception as e:
                print_colored(f"❌ Error scanning {pair}: {str(e)}", "ERROR")

        # Summary
        print()
        if signals_found > 0:
            print_colored(f"✅ Found {signals_found} trading signals", "SUCCESS", bold=True)
        else:
            print_colored("ℹ️  No trading signals found", "INFO")
        print()
    
    def log_signal(self, formatted_output, signal_data):
        """Log signal information in horizontal format with colors and emojis"""
        # Get direction emoji and color
        if signal_data['signal'] == 'BUY':
            direction_emoji = "📈"
            direction_color = "BUY"
        else:
            direction_emoji = "📉"
            direction_color = "SELL"

        # Format with proper spacing and emojis
        pair_part = f"💱 {formatted_output['pair']:<10}"
        date_part = f"📅 {formatted_output['date']}"
        time_part = f"🕐 {formatted_output['time']}"
        direction_part = f"{direction_emoji} {signal_data['signal']:<4}"
        confidence_part = f"🎯 {formatted_output['confidence']:<8}"
        price_part = f"💰 {formatted_output['price']:<10}"
        strategy_part = f"🔧 {signal_data['strategy']}"

        # Create the formatted line with proper spacing
        signal_line = f"{pair_part} | {date_part} | {time_part} | {direction_part} | {confidence_part} | {price_part} | {strategy_part}"
        print_colored(signal_line, direction_color, bold=True)

def main():
    """Main function"""
    try:
        # Create and start the trading bot
        bot = LiveTradingBot()
        
        print_colored("🤖 Live Trading Bot Initializing...", "INFO", bold=True)
        print_colored("Press Ctrl+C to stop the bot", "WARNING")
        print()
        
        # Start the bot
        bot.start()
        
    except KeyboardInterrupt:
        print_colored("\n👋 Goodbye! Trading bot stopped.", "INFO")
    except Exception as e:
        print_colored(f"❌ Fatal error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()

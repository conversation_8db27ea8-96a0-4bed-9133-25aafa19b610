# 🚀 How to Run the Trading Bot

## Quick Start Guide

### **Step 1: Open Terminal/Command Prompt**
Navigate to your bot directory:
```bash
cd "C:\Users\<USER>\OneDrive\Desktop\100% Working\Train Bot"
```

### **Step 2: Run the Main Launcher**
```bash
python trading_bot_launcher.py
```

## 📋 **Available Options**

When you run the launcher, you'll see this menu:

```
🤖 ADVANCED TRADING BOT SYSTEM
Choose an option:

1. 📈 Rule-Based Trading (Live)
2. 🧠 ML-Integrated Trading (Live) 
3. 🔬 Backtesting System
4. 🔗 Test API Connection
5. 📊 View Current Directory
6. ❌ Exit
```

## 🎯 **Option Details**

### **Option 1: Rule-Based Trading (Live)**
- Uses pure rule-based strategies (S1, S2, S3, S4)
- Fast execution with transparent logic
- Shows signals in the new colorful format
- Monitors all currency pairs every minute

**What you'll see:**
```
💱 PAIR            |   📅 DATE         |   🕐 TIME       |   📈📉 DIRECTION   |   🎯 CONFIDENCE   |   💰 PRICE        |   🔧 STRATEGY     
========================================================================================================================
💱 EUR_USD        |   📅 2025-06-03     |   🕐 03:27:55     |   📈 BUY          |   🎯 80.00%       |   💰 1.08450        |   🔧 S1              
💱 EUR_GBP        |   📅 2025-06-03     |   🕐 03:27:55     |   ⚪ NO SIGNAL    |   🎯 -            |   💰 0.83245        |   🔧 -                
```

### **Option 2: ML-Integrated Trading (Live)**
- Uses Machine Learning + Rule-based strategies
- AI predictions with confidence scoring
- Enhanced accuracy with ML models
- Same colorful format with 🤖 indicators

**What you'll see:**
```
💱 PAIR            |   📅 DATE         |   🕐 TIME       |   📈📉 DIRECTION   |   🎯 CONFIDENCE   |   💰 PRICE        |   🤖 STRATEGY     
========================================================================================================================
💱 USD_CAD        |   📅 2025-06-03     |   🕐 03:27:55     |   📈 BUY          |   🎯 85.50%       |   💰 1.37148        |   🤖 ML + Rule       
💱 AUD_USD        |   📅 2025-06-03     |   🕐 03:27:55     |   🤖 NO SIGNAL    |   🎯 -            |   💰 0.65890        |   🤖 -                
```

### **Option 3: Backtesting System**
Sub-menu with options:
- **Rule-Based Backtesting**: Test pure strategies
- **ML-Integrated Backtesting**: Test ML-enhanced strategies  
- **Comparative Backtesting**: Compare rule-based vs ML performance

### **Option 4: Test API Connection**
- Verifies your Oanda API connection
- Tests authentication and data fetching
- Useful for troubleshooting

### **Option 5: View Current Directory**
- Shows all files in your bot directory
- Organized by file type (Python, CSV, Models, etc.)

## 🔧 **Alternative Direct Methods**

### **Run Rule-Based Bot Directly:**
```bash
python live_trading_bot.py
```

### **Run ML-Integrated Bot Directly:**
```bash
python ml_trading_bot.py
```

### **Run Backtesting:**
```bash
python backtest_system.py
```

### **Run ML Backtesting:**
```bash
python ml_backtest_system.py
```

## ⚠️ **Important Notes**

### **Before Running:**
1. **Check API Configuration**: Make sure your Oanda API credentials are set in `config.py`
2. **Internet Connection**: Required for live data fetching
3. **Python Dependencies**: Ensure all required packages are installed

### **During Execution:**
- **Live Trading**: Fetches data every 58 seconds (2 seconds before next candle)
- **Stop Anytime**: Press `Ctrl+C` to stop the bot safely
- **Monitor Output**: Watch for signals in the colorful format

### **What Each Color Means:**
- 💱 **Cyan**: Currency pairs
- 📅 **Yellow**: Dates  
- 🕐 **Magenta**: Times
- 📈 **Green**: BUY signals
- 📉 **Red**: SELL signals
- 🎯 **Blue**: Confidence percentages
- 💰 **White**: Prices
- 🔧🤖 **Gray**: Strategies

## 🎯 **Recommended Workflow**

1. **First Time**: Run Option 4 to test API connection
2. **For Live Trading**: Choose Option 1 (Rule-based) or Option 2 (ML-integrated)
3. **For Testing**: Use Option 3 for backtesting
4. **Monitor**: Watch the colorful signal display every minute

## 🛑 **How to Stop**

- Press `Ctrl+C` in the terminal
- The bot will stop safely and show a goodbye message
- All processes will terminate cleanly

## 📊 **Expected Output**

Every minute you'll see:
1. **Header line** with column names
2. **Signal data** for each currency pair with different colors
3. **Summary** showing how many signals were found

The new format makes it easy to quickly scan and identify trading opportunities!

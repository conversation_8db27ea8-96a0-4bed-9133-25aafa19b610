#!/usr/bin/env python3
"""
Demo script to show the new horizontal signal format
"""

from utils import print_colored, print_header
from datetime import datetime

def demo_horizontal_signals():
    """Demonstrate the new horizontal signal format"""
    print_header("🎯 NEW HORIZONTAL SIGNAL FORMAT DEMO")
    
    # Current time for demo
    current_time = datetime.now()
    date_str = current_time.strftime('%Y-%m-%d')
    time_str = current_time.strftime('%H:%M:%S')
    
    print_colored("📊 Example signals in new horizontal format:", "INFO", bold=True)
    print()
    
    # Example 1: Signal found
    print_colored("✅ When SIGNAL is found:", "SUCCESS", bold=True)
    signal_line = f"{'EUR_USD':<12} {date_str} {time_str} {'SELL':<4} {'70.00%':<8} {'1.08450':<10} S4"
    print_colored(signal_line, "SELL", bold=True)
    
    signal_line = f"{'GBP_JPY':<12} {date_str} {time_str} {'BUY':<4} {'85.50%':<8} {'163.202':<10} S2"
    print_colored(signal_line, "BUY", bold=True)
    
    print()
    
    # Example 2: No signal found
    print_colored("ℹ️  When NO SIGNAL is found:", "INFO", bold=True)
    no_signal_line = f"{'USD_CAD':<12} {date_str} {time_str} {'1.37148':<10} NO SIGNAL FOUND"
    print_colored(no_signal_line, "INFO")
    
    no_signal_line = f"{'AUD_USD':<12} {date_str} {time_str} {'0.65890':<10} NO SIGNAL FOUND"
    print_colored(no_signal_line, "INFO")
    
    print()
    
    # Example 3: Error case
    print_colored("❌ When DATA ERROR occurs:", "ERROR", bold=True)
    error_line = f"{'EUR_GBP':<12} {date_str} {time_str} ERROR      NO DATA AVAILABLE"
    print_colored(error_line, "ERROR")
    
    print()
    print_colored("📋 Format explanation:", "HEADER", bold=True)
    print_colored("   • PAIR_NAME: 12 characters wide, left-aligned", "INFO")
    print_colored("   • DATE: YYYY-MM-DD format", "INFO")
    print_colored("   • TIME: HH:MM:SS format", "INFO")
    print_colored("   • DIRECTION: BUY/SELL (4 chars wide)", "INFO")
    print_colored("   • CONFIDENCE: XX.XX% (8 chars wide)", "INFO")
    print_colored("   • PRICE: Current market price (10 chars wide)", "INFO")
    print_colored("   • STRATEGY: Strategy name (S1, S2, S3, S4, etc.)", "INFO")
    print()
    
    print_colored("🔄 Comparison with old format:", "HEADER", bold=True)
    print_colored("OLD (Vertical):", "WARNING", bold=True)
    print_colored("   SIGNAL DETECTED:", "SUCCESS")
    print_colored("      📅 Date: 2025-06-03", "INFO")
    print_colored("      🕐 Time: 02:49:00", "INFO")
    print_colored("      💱 Pair: USD_CAD", "INFO")
    print_colored("      📈 Direction: SELL", "SELL")
    print_colored("      💰 Price: 1.37148", "INFO")
    print_colored("      🎯 Confidence: 70.00%", "SUCCESS")
    print_colored("      🔧 Strategy: S4", "INFO")
    print()
    
    print_colored("NEW (Horizontal):", "SUCCESS", bold=True)
    signal_line = f"{'USD_CAD':<12} 2025-06-03 02:49:00 {'SELL':<4} {'70.00%':<8} {'1.37148':<10} S4"
    print_colored(signal_line, "SELL", bold=True)
    print()
    
    print_colored("✨ Benefits of new format:", "SUCCESS", bold=True)
    print_colored("   • More compact and easier to scan", "INFO")
    print_colored("   • All pairs visible at once", "INFO")
    print_colored("   • Quick comparison between pairs", "INFO")
    print_colored("   • Less screen space used", "INFO")
    print_colored("   • Better for monitoring multiple pairs", "INFO")

if __name__ == "__main__":
    demo_horizontal_signals()

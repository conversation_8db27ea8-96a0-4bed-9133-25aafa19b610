#!/usr/bin/env python3
"""
ML-Integrated Live Trading Bot with Oanda API Integration
Combines Machine Learning predictions with rule-based strategies
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from ml_strategy_engine import MLStrategyEngine
from utils import (
    fetch_live_candles, get_current_time_info, print_colored, 
    print_header, print_table_row, format_signal_output, validate_pair
)
from config import CURRENCY_PAIRS, TRADING_CONFIG, DISPLAY_CONFIG

class MLTradingBot:
    def __init__(self):
        """Initialize the ML-integrated trading bot"""
        self.strategy_engine = MLStrategyEngine()
        self.running = False
        self.pairs = CURRENCY_PAIRS.copy()
        
    def start(self):
        """Start the ML-integrated trading bot"""
        print_header("🧠 ML-INTEGRATED TRADING BOT STARTED")
        print_colored(f"🤖 AI-Enhanced: Machine Learning + Rule-based strategies", "SUCCESS", bold=True)
        print_colored(f"📊 Monitoring {len(self.pairs)} currency pairs", "INFO")
        print_colored(f"⏰ Fetching data every {TRADING_CONFIG['FETCH_INTERVAL']} seconds", "INFO")
        print_colored(f"🎯 ML Confidence threshold: {TRADING_CONFIG['MIN_CONFIDENCE']*100}%", "INFO")
        print_colored(f"🔧 Enhanced with ensemble AI models", "SUCCESS")
        print()
        
        self.running = True
        
        # Start the main trading loop
        self.trading_loop()
    
    def stop(self):
        """Stop the trading bot"""
        self.running = False
        print_colored("\n🛑 ML Trading bot stopped", "WARNING")
    
    def trading_loop(self):
        """Main trading loop"""
        while self.running:
            try:
                time_info = get_current_time_info()
                
                # Wait until 2 seconds before next minute
                if time_info['seconds_to_next_minute'] > 2:
                    sleep_time = time_info['seconds_to_next_minute'] - 2
                    print_colored(f"⏳ AI analysis in {sleep_time} seconds...", "INFO")
                    time.sleep(sleep_time)
                
                # Perform ML-enhanced market scan
                self.scan_markets_with_ml()
                
                # Wait for next minute
                time.sleep(62)  # Wait a bit more than a minute to ensure new candle
                
            except KeyboardInterrupt:
                print_colored("\n⚠️  Interrupted by user", "WARNING")
                break
            except Exception as e:
                print_colored(f"❌ Error in ML trading loop: {str(e)}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def scan_markets_with_ml(self):
        """Scan all currency pairs for trading signals using ML"""
        current_time = datetime.now()
        print_header(f"🧠 ML-ENHANCED MARKET SCAN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        signals_found = 0

        for pair in self.pairs:
            try:
                # Fetch live data
                df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])

                if df is not None and len(df) > 50:
                    # Evaluate with ML-enhanced strategies
                    signal_data = self.strategy_engine.evaluate_ml_enhanced_strategies(df)

                    # Format output
                    formatted_output = format_signal_output(pair, signal_data)

                    # Display result in horizontal format
                    if signal_data['signal'] != 'HOLD':
                        signals_found += 1
                        # Log ML signal in horizontal format
                        self.log_ml_signal(formatted_output, signal_data)
                    else:
                        # No signal found - show in horizontal format
                        no_signal_line = f"{pair:<12} {formatted_output['date']} {formatted_output['time']} {formatted_output['price']:<10} NO SIGNAL FOUND"
                        print_colored(no_signal_line, "INFO")
                else:
                    # Data fetch failed
                    error_line = f"{pair:<12} {current_time.strftime('%Y-%m-%d')} {current_time.strftime('%H:%M:%S')} ERROR      NO DATA AVAILABLE"
                    print_colored(error_line, "ERROR")

            except Exception as e:
                print_colored(f"❌ Error scanning {pair}: {str(e)}", "ERROR")

        # Summary
        print()
        if signals_found > 0:
            print_colored(f"🤖 Found {signals_found} ML-enhanced trading signals", "SUCCESS", bold=True)
        else:
            print_colored("🤖 No ML-enhanced trading signals found", "INFO")
        print()
    
    def log_ml_signal(self, formatted_output, signal_data):
        """Log ML signal information in horizontal format"""
        # Format: PAIR_NAME DATE TIME DIRECTION CONFIDENCE PRICE ML_METHOD
        ml_method = signal_data.get('decision_method', 'ML+Rule')
        signal_line = f"{formatted_output['pair']:<12} {formatted_output['date']} {formatted_output['time']} {signal_data['signal']:<4} {formatted_output['confidence']:<8} {formatted_output['price']:<10} {ml_method}"
        print_colored(signal_line, formatted_output['color'], bold=True)

def main():
    """Main function"""
    try:
        # Create and start the ML trading bot
        bot = MLTradingBot()
        
        print_colored("🤖 ML-Integrated Trading Bot Initializing...", "SUCCESS", bold=True)
        print_colored("🧠 Loading AI models and rule-based strategies...", "INFO")
        print_colored("Press Ctrl+C to stop the bot", "WARNING")
        print()
        
        # Start the bot
        bot.start()
        
    except KeyboardInterrupt:
        print_colored("\n👋 Goodbye! ML Trading bot stopped.", "INFO")
    except Exception as e:
        print_colored(f"❌ Fatal error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
